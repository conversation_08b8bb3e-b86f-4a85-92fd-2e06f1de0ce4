import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { FaArrowRight, FaEye, FaMapMarkerAlt } from 'react-icons/fa';

// Example jobs data (should be replaced with real data or fetched from API)
const jobs = [
  {
    id: 1,
    company: 'Irdeto',
    title: 'Software Engineer',
    location: 'Noida',
    views: 36,
    activelyHiring: true,
    mode: 'In Office',
    color: 'bg-blue-200',
    logo: 'https://logo.clearbit.com/irdeto.com',
    category: 'Software Development',
    description: 'Develop and maintain software applications using modern technologies.',
    salary: '10-15 LPA',
    impressions: 1200,
    deadline: '2025-08-05T00:00:00Z',
    eligibility: 'Fresher, Graduate',
  },
  {
    id: 2,
    company: 'NetApp',
    title: 'Renewal Quoting Specialist',
    location: 'Bengaluru',
    views: 14,
    activelyHiring: true,
    mode: 'In Office',
    color: 'bg-pink-200',
    logo: 'https://logo.clearbit.com/netapp.com',
    category: 'Finance',
    description: 'Handle client renewals and quoting processes.',
    salary: '8-12 LPA',
    impressions: 800,
    deadline: '2025-08-10T00:00:00Z',
    eligibility: 'Experienced Professionals',
  },
  {
    id: 3,
    company: 'Plum',
    title: 'Client Onboarding Specialist',
    location: 'Bangalore Urban',
    views: 19,
    activelyHiring: true,
    mode: 'In Office',
    color: 'bg-purple-200',
    logo: 'https://logo.clearbit.com/plumhq.com',
    category: 'Marketing',
    description: 'Onboard new clients and ensure a smooth transition.',
    salary: '7-10 LPA',
    impressions: 600,
    deadline: '2025-08-15T00:00:00Z',
    eligibility: 'Graduate',
  },
  {
    id: 4,
    company: 'Puma Energy',
    title: 'HR Technology Analyst',
    location: 'Mumbai',
    views: 14,
    activelyHiring: true,
    mode: 'In Office',
    color: 'bg-yellow-200',
    logo: 'https://logo.clearbit.com/pumaenergy.com',
    category: 'Software Development',
    description: 'Analyze and improve HR technology systems.',
    salary: '9-13 LPA',
    impressions: 900,
    deadline: '2025-08-20T00:00:00Z',
    eligibility: 'Experienced Professionals',
  },
];

export default function JobsCategory() {
  const { categoryName } = useParams();
  const filteredJobs = jobs.filter(job => job.category.toLowerCase() === decodeURIComponent(categoryName).toLowerCase());
  const [selectedJob, setSelectedJob] = useState(filteredJobs[0] || null);

  return (
    <div className="flex h-[calc(100vh-60px)] bg-gray-50">
      {/* Left: Job List */}
      <div className="w-1/3 border-r bg-white overflow-y-auto p-4">
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-1">{categoryName} Jobs</h2>
            <div className="text-gray-500">Showing jobs for category: <span className="font-semibold">{categoryName}</span></div>
          </div>
          <Link to="/student/jobs" className="text-blue-700 font-semibold flex items-center gap-2 hover:underline">Back <FaArrowRight /></Link>
        </div>
        <div className="flex flex-col gap-4">
          {filteredJobs.length === 0 ? (
            <div className="text-gray-500">No jobs found for this category.</div>
          ) : (
            filteredJobs.map(job => (
              <div
                key={job.id}
                className={`rounded-xl p-4 shadow cursor-pointer border transition ${selectedJob && selectedJob.id === job.id ? 'border-blue-600 bg-blue-50' : 'border-gray-200 bg-white hover:border-blue-400'}`}
                onClick={() => setSelectedJob(job)}
              >
                <div className="flex items-center gap-3 mb-2">
                  <img src={job.logo} alt={job.company} className="w-10 h-10 rounded bg-white shadow" />
                  <div>
                    <div className="font-bold text-lg text-gray-800">{job.title}</div>
                    <div className="text-gray-500 text-sm">{job.company}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-gray-500 text-xs">
                  <FaMapMarkerAlt /> {job.location}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
      {/* Right: Job Details */}
      <div className="flex-1 overflow-y-auto p-8">
        {selectedJob ? (
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="flex items-center gap-4 mb-6">
              <img src={selectedJob.logo} alt={selectedJob.company} className="w-16 h-16 rounded-xl bg-white shadow" />
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-1">{selectedJob.title}</h2>
                <div className="text-gray-500">{selectedJob.company}</div>
              </div>
            </div>
            <div className="flex gap-8 mb-6">
              <div className="text-gray-600 flex items-center gap-2"><span>📍</span>{selectedJob.location}</div>
              <div className="text-gray-600 flex items-center gap-2"><span>👁️</span>{selectedJob.views} Views</div>
            </div>
            <div className="mb-4">
              <span className="font-semibold">Salary:</span> {selectedJob.salary}
            </div>
            <div className="mb-4">
              <span className="font-semibold">Impressions:</span> {selectedJob.impressions}
            </div>
            <div className="mb-4">
              <span className="font-semibold">Application Deadline:</span> {new Date(selectedJob.deadline).toLocaleString()}
            </div>
            <div className="mb-4">
              <span className="font-semibold">Eligibility:</span> {selectedJob.eligibility}
            </div>
            <div className="mb-4">
              <span className="font-semibold">Job Description:</span>
              <div className="mt-2 text-gray-700">{selectedJob.description}</div>
            </div>
            <button className="bg-blue-700 text-white px-6 py-2 rounded-full font-bold mt-4 hover:bg-blue-800 transition">Quick Apply</button>
          </div>
        ) : (
          <div className="text-gray-500">Select a job to see details.</div>
        )}
      </div>
    </div>
  );
} 