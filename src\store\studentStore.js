import { create } from 'zustand';
import axios from 'axios';
import { STUDENT_ENDPOINTS } from '../lib/constants';

const axiosInstance = axios.create({
    withCredentials: true,
});

axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Add response interceptor to handle token expiration
axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            localStorage.removeItem('token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

// Default profile structure
const defaultProfile = {
    name: '',
    email: '',
    address: '',
    avatar: '',
    about: '',
    resume: '',
    skills: [],
    work: [],
    education: [],
    responsibilities: [],
    certificates: [],
    projects: [],
    achievements: [],
    social: [],
    streaks: [],
};

const useStudentStore = create((set, get) => ({
    // Loading states
    loading: false,
    profileLoading: false,
    jobsLoading: false,
    applicationsLoading: false,
    testsLoading: false,

    // Error states
    error: null,
    profileError: null,
    jobsError: null,
    applicationsError: null,
    testsError: null,

    // Data states
    profile: defaultProfile,
    jobs: [],
    jobDetails: null,
    applications: [],
    tests: {
        upcoming: [],
        live: [],
        history: []
    },
    results: [],

    // Pagination and filters
    jobsFilter: {
        tech: '',
        category: '',
        jobType: '',
        experienceLevel: '',
        workMode: '',
        location: '',
        salaryMin: '',
        salaryMax: '',
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
    },
    jobsPagination: {
        currentPage: 1,
        totalPages: 0,
        totalJobs: 0,
        hasNextPage: false,
        hasPrevPage: false,
        limit: 10
    },

    // Basic setters
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),
    clearError: () => set({ error: null, profileError: null, jobsError: null, applicationsError: null, testsError: null }),

    // Profile Management
    setProfile: (profile) => set({ profile }),
    updateProfile: (updates) => set((state) => ({
        profile: { ...state.profile, ...updates }
    })),

    // Profile API methods
    fetchProfile: async () => {
        set({ profileLoading: true, profileError: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.PROFILE);
            const profileData = data.success ? data.data : data;
            set({
                profile: { ...defaultProfile, ...profileData },
                profileLoading: false
            });
            return profileData;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch profile';
            set({
                profileError: errorMsg,
                profileLoading: false
            });
            return null;
        }
    },

    saveProfile: async (profileData) => {
        set({ profileLoading: true, profileError: null });
        try {
            const { data } = await axiosInstance.put(STUDENT_ENDPOINTS.PROFILE, profileData);
            const updatedProfile = data.success ? data.data : data;
            set({
                profile: { ...get().profile, ...updatedProfile },
                profileLoading: false
            });
            return updatedProfile;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to save profile';
            set({
                profileError: errorMsg,
                profileLoading: false
            });
            throw error;
        }
    },

    // Jobs Management
    setJobsFilter: (filter) => set((state) => ({
        jobsFilter: { ...state.jobsFilter, ...filter }
    })),

    fetchJobs: async (filters = {}) => {
        set({ jobsLoading: true, jobsError: null });
        try {
            const currentFilter = { ...get().jobsFilter, ...filters };
            const queryParams = new URLSearchParams();

            Object.entries(currentFilter).forEach(([key, value]) => {
                if (value !== '' && value !== null && value !== undefined) {
                    queryParams.append(key, value);
                }
            });

            const { data } = await axiosInstance.get(`${STUDENT_ENDPOINTS.JOBS}?${queryParams}`);

            if (data.success) {
                set({
                    jobs: data.data.jobs,
                    jobsPagination: data.data.pagination,
                    jobsFilter: currentFilter,
                    jobsLoading: false
                });
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch jobs';
            set({
                jobsError: errorMsg,
                jobsLoading: false
            });
            return null;
        }
    },

    fetchJobDetails: async (jobId) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.JOB_DETAILS(jobId));
            const jobDetails = data.success ? data.data : data;
            set({
                jobDetails,
                loading: false
            });
            return jobDetails;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch job details';
            set({
                error: errorMsg,
                loading: false
            });
            return null;
        }
    },

    applyToJob: async (jobId) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(STUDENT_ENDPOINTS.APPLY_TO_JOB(jobId));

            // Update the job details if it's currently loaded
            const currentJobDetails = get().jobDetails;
            if (currentJobDetails && currentJobDetails.job._id === jobId) {
                set({
                    jobDetails: {
                        ...currentJobDetails,
                        hasApplied: true,
                        canApply: false
                    }
                });
            }

            // Refresh applications
            get().fetchApplications();

            set({ loading: false });
            return data;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to apply to job';
            set({
                error: errorMsg,
                loading: false
            });
            throw error;
        }
    },

    // Applications Management
    fetchApplications: async (filters = {}) => {
        set({ applicationsLoading: true, applicationsError: null });
        try {
            const queryParams = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== '' && value !== null && value !== undefined) {
                    queryParams.append(key, value);
                }
            });

            const { data } = await axiosInstance.get(`${STUDENT_ENDPOINTS.APPLICATIONS}?${queryParams}`);

            if (data.success) {
                set({
                    applications: data.data.applications,
                    applicationsLoading: false
                });
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch applications';
            set({
                applicationsError: errorMsg,
                applicationsLoading: false
            });
            return null;
        }
    },

    // Tests Management
    fetchUpcomingTests: async () => {
        set({ testsLoading: true, testsError: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.UPCOMING_TESTS);

            if (data.success) {
                set((state) => ({
                    tests: { ...state.tests, upcoming: data.data },
                    testsLoading: false
                }));
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch upcoming tests';
            set({
                testsError: errorMsg,
                testsLoading: false
            });
            return null;
        }
    },

    fetchLiveTests: async () => {
        set({ testsLoading: true, testsError: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.TEST_LIVE());

            if (data.success) {
                set((state) => ({
                    tests: { ...state.tests, live: data.data },
                    testsLoading: false
                }));
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch live tests';
            set({
                testsError: errorMsg,
                testsLoading: false
            });
            return null;
        }
    },

    fetchTestHistory: async () => {
        set({ testsLoading: true, testsError: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.TEST_HISTORY);

            if (data.success) {
                set((state) => ({
                    tests: { ...state.tests, history: data.data },
                    testsLoading: false
                }));
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch test history';
            set({
                testsError: errorMsg,
                testsLoading: false
            });
            return null;
        }
    },

    fetchTestDetails: async (testId) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.TEST_DETAILS(testId));
            const testDetails = data.success ? data.data : data;
            set({
                loading: false
            });
            return testDetails;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch test details';
            set({
                error: errorMsg,
                loading: false
            });
            return null;
        }
    },

    submitTest: async (testId, answers, additionalData = {}) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(STUDENT_ENDPOINTS.SUBMIT_TEST(testId), {
                answers,
                ...additionalData
            });

            // Refresh test history after submission
            get().fetchTestHistory();

            set({ loading: false });
            return data;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to submit test';
            set({
                error: errorMsg,
                loading: false
            });
            throw error;
        }
    },

    // Results Management
    fetchResults: async () => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.RESULTS);
            const results = data.success ? data.data : data;
            set({
                results,
                loading: false
            });
            return results;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch results';
            set({
                error: errorMsg,
                loading: false
            });
            return null;
        }
    },

    // Utility methods
    getProfileCompletion: () => {
        const profile = get().profile;
        const sectionKeys = ['name', 'email', 'address', 'about', 'resume', 'skills', 'work', 'education', 'responsibilities', 'certificates', 'projects', 'achievements'];

        const filledSections = sectionKeys.filter(key => {
            const val = profile[key];
            if (Array.isArray(val)) return val.length > 0;
            return !!val && val !== '';
        }).length;

        return Math.round((filledSections / sectionKeys.length) * 100);
    },

    // Reset methods
    resetJobs: () => set({ jobs: [], jobDetails: null, jobsPagination: { currentPage: 1, totalPages: 0, totalJobs: 0, hasNextPage: false, hasPrevPage: false, limit: 10 } }),
    resetApplications: () => set({ applications: [] }),
    resetTests: () => set({ tests: { upcoming: [], live: [], history: [] } }),
    resetResults: () => set({ results: [] }),
    resetAll: () => set({
        profile: defaultProfile,
        jobs: [],
        jobDetails: null,
        applications: [],
        tests: { upcoming: [], live: [], history: [] },
        results: [],
        error: null,
        profileError: null,
        jobsError: null,
        applicationsError: null,
        testsError: null
    })
}));

export default useStudentStore;