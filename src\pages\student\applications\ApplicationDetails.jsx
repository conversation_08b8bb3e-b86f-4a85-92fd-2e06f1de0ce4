import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaArrowLeft, FaShareAlt, FaUser, FaUsers, FaEye, FaCalendarAlt } from 'react-icons/fa';

const mockApplications = [
  {
    id: 1,
    title: 'Direct Sales Executive',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    updatedOn: 'Jan 10, 2025',
    registeredOn: '07 Jan 25, 06:45 PM IST',
    deadline: '10 Jan 25, 07:44 AM IST',
    status: 'Closed',
    steps: ['Registration Form', 'Extended Form'],
    completedSteps: [true, true],
    notifications: false,
    completed: true,
    email: '<EMAIL>',
    category: 'Jobs',
    eligibility: 'Everyone can apply',
    description: `**MEDINI is seeking a highly motivated and results-oriented Direct Sales Executive to join our growing team!**\n\n**Responsibilities of the Candidate:**\n- Prospect and qualify potential clients through various channels, including phone, email, and social media.\n- Develop and maintain strong relationships with clients to understand their needs and build trust.\n- Present and demonstrate the value proposition of <PERSON><PERSON> Mate's products and services.\n- Negotiate and close deals, exceeding individual and team sales targets.\n- Provide excellent customer service and support to ensure client satisfaction.\n- Stay up-to-date on industry trends and competitor offerings.\n- Contribute to the development and implementation of sales strategies and initiatives.\n- Maintain accurate records and reports of sales activities and results.`,
    applied: 560,
    impressions: 44544,
    refer: true,
    user: {
      name: 'Abhishek E B',
      email: '<EMAIL>',
      eligible: true,
      avatar: '',
    },
  },
  {
    id: 2,
    title: 'Python Internship',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    updatedOn: 'Nov 15, 2024',
    registeredOn: '26 Sep 24, 10:26 PM IST',
    deadline: '15 Nov 24, 11:59 PM IST',
    status: 'Closed',
    steps: ['Registration Form', 'Extended Form'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Internships',
    eligibility: 'Everyone can apply',
    description: `**Internship Mela is looking for Python Interns!**\n\n**Responsibilities:**\n- Assist in developing Python applications.\n- Collaborate with the team on new features.\n- Write clean, maintainable code.\n- Participate in code reviews.\n- Learn and apply new technologies.`,
    applied: 120,
    impressions: 12000,
    refer: true,
    user: {
      name: 'Abhishek E B',
      email: '<EMAIL>',
      eligible: true,
      avatar: '',
    },
  },
  {
    id: 3,
    title: 'MEDINI Hiring Challenge 2024',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    updatedOn: 'Sep 19, 2024',
    registeredOn: '13 Sep 24, 04:23 PM IST',
    deadline: '19 Sep 24, 11:59 PM IST',
    status: 'Closed',
    steps: ['Registration Form', 'Face Verification'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Competitions',
    eligibility: 'Everyone can apply',
    description: `**MEDINI is conducting a Hiring Challenge for 2024!**\n\n**Responsibilities:**\n- Solve algorithmic problems.\n- Participate in online coding rounds.\n- Face verification for shortlisted candidates.\n- Collaborate with mentors and peers.`,
    applied: 300,
    impressions: 25000,
    refer: true,
    user: {
      name: 'Abhishek E B',
      email: '<EMAIL>',
      eligible: true,
      avatar: '',
    },
  },
  {
    id: 4,
    title: 'MEDINI Imagination Challenge 2024: Student Track',
    company: 'MEDINI',
    logo: 'https://logo.clearbit.com/MEDINI.COM',
    updatedOn: 'Sep 9, 2024',
    registeredOn: '25 Aug 24, 07:39 PM IST',
    deadline: '09 Sep 24, 11:59 PM IST',
    status: 'Closed',
    steps: ['Registration Form', 'Face Verification'],
    completedSteps: [true, true],
    notifications: true,
    completed: true,
    email: '<EMAIL>',
    category: 'Competitions',
    eligibility: 'Everyone can apply',
    description: `**MEDINI Imagination Challenge 2024: Student Track**\n\n**Responsibilities:**\n- Register and complete the face verification.\n- Submit your innovative ideas.\n- Participate in the challenge rounds.\n- Network with industry leaders.`,
    applied: 200,
    impressions: 18000,
    refer: true,
    user: {
      name: 'Abhishek E B',
      email: '<EMAIL>',
      eligible: true,
      avatar: '',
    },
  },
];

const tabs = [
  'Job Description',
  'Dates & Deadlines',
  'Reviews',
  'FAQs & Discussions',
];

export default function ApplicationDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(tabs[0]);
  const app = mockApplications.find(a => a.id === Number(id));

  if (!app) return <div className="p-8 text-gray-500">Application not found.</div>;

  return (
    <div className="min-h-screen bg-[#23414c]/5 py-6 px-2 md:px-8">
      <div className="max-w-6xl mx-auto flex flex-col md:flex-row gap-8">
        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <button onClick={() => navigate(-1)} className="flex items-center gap-2 text-[#23414c] font-semibold mb-6 hover:underline"><FaArrowLeft /> Back</button>
          <div className="bg-white rounded-xl shadow p-6 mb-6">
            <div className="flex items-center gap-4 mb-4">
              <img src={app.logo} alt={app.company} className="w-20 h-20 rounded bg-white border object-contain" />
              <div>
                <div className="font-bold text-3xl text-gray-800 mb-1">{app.title}</div>
                <div className="flex items-center gap-4 text-gray-500 text-sm">
                  <span>{app.company}</span>
                  <span className="flex items-center gap-1"><FaCalendarAlt className="inline" /> Updated On: {app.updatedOn}</span>
                </div>
              </div>
            </div>
            {/* Tabs */}
            <div className="flex gap-4 border-b mb-4 mt-4">
              {tabs.map(tab => (
                <button
                  key={tab}
                  className={`pb-2 px-2 font-semibold whitespace-nowrap border-b-2 transition-all ${activeTab === tab ? 'border-[#23414c] text-[#23414c]' : 'border-transparent text-gray-500 hover:text-[#23414c]'}`}
                  onClick={() => setActiveTab(tab)}
                >
                  {tab}
                </button>
              ))}
            </div>
            {/* Info Alert */}
            <div className="bg-[#23414c]/10 border-l-4 border-[#23414c] text-[#23414c] px-4 py-3 mb-4 rounded">
              Hire Mate is no longer accepting applications for Direct Sales Executive
            </div>
            {/* Details Section */}
            {activeTab === 'Job Description' && (
              <div className="bg-[#23414c]/5 rounded-lg p-4">
                <div className="font-bold text-xl mb-2 text-[#23414c]">Details</div>
                <div className="prose max-w-none text-gray-800">
                  <Markdown>{app.description}</Markdown>
                </div>
              </div>
            )}
            {/* You can add more tab content here as needed */}
          </div>
        </div>
        {/* Sidebar */}
        <aside className="w-full md:w-96 flex-shrink-0 flex flex-col gap-4">
          {/* User Card */}
          <div className="bg-white rounded-xl shadow p-6 flex flex-col gap-2 items-center">
            <div className="flex items-center gap-3 w-full justify-between">
              <div>
                <div className="font-bold text-gray-800">{app.user.name}</div>
                <div className="text-xs text-gray-500">{app.user.email}</div>
              </div>
              <span className="px-3 py-1 rounded bg-green-100 text-green-700 text-xs font-bold">Eligible</span>
            </div>
            <div className="flex items-center gap-2 w-full justify-between mt-2">
              <button className="text-gray-400 hover:text-red-500"><FaUser /></button>
              <button className="flex items-center gap-2 px-4 py-2 border rounded-lg text-gray-700 hover:bg-gray-50"><FaShareAlt /> Share</button>
            </div>
            <button className="w-full bg-green-600 text-white font-bold py-2 rounded-lg mt-4">You've Applied<br /><span className="text-xs font-normal">(Check Your Status)</span></button>
            <div className="flex flex-col gap-2 w-full mt-4">
              <div className="flex items-center gap-2 text-gray-600"><FaUsers /> <span className="font-semibold">Applied</span> <span className="ml-auto">560</span></div>
              <div className="flex items-center gap-2 text-gray-600"><FaEye /> <span className="font-semibold">Impressions</span> <span className="ml-auto">44,544</span></div>
              <div className="flex items-center gap-2 text-gray-600"><FaCalendarAlt /> <span className="font-semibold">Application Deadline</span> <span className="ml-auto">10 Jan 25, 07:44 AM IST</span></div>
            </div>
          </div>
          {/* Eligibility Card */}
          <div className="bg-white rounded-xl shadow p-4">
            <div className="font-bold mb-2 text-[#23414c]">Eligibility</div>
            <div className="text-gray-700">{app.eligibility}</div>
          </div>
          {/* Refer & Win Card */}
          <div className="bg-white rounded-xl shadow p-4 flex flex-col gap-2">
            <div className="font-bold mb-1 text-[#23414c]">Refer & Win</div>
            <div className="text-xs text-gray-600">MacBook, iPhone, Apple Watch, Cash and more!</div>
            <button className="mt-2 px-4 py-2 rounded-full border border-[#23414c] text-[#23414c] font-semibold hover:bg-[#23414c]/10 transition">Refer now</button>
            <button className="text-xs text-[#23414c] hover:underline">Know more</button>
          </div>
          {/* Ad/Banner Card */}
          <div className="bg-white rounded-xl shadow p-4 flex flex-col items-center">
            <img src="https://assets9.lottiefiles.com/packages/lf20_3rwasyjy.json" alt="Unstop Pro" className="w-32 h-24 object-contain mb-2" />
            <div className="font-bold text-[#23414c] mb-1">Unstop Pro</div>
            <div className="text-xs text-gray-600 mb-2">50+ Courses | 15% Off Mentorship | 100+ Interview prep</div>
            <button className="bg-[#23414c] text-white px-4 py-2 rounded-full font-bold">Go Pro Now</button>
          </div>
        </aside>
      </div>
    </div>
  );
}

// Markdown renderer for description
function Markdown({ children }) {
  // Simple markdown to HTML for bullets and bold
  const html = children
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\n- /g, '<ul><li>')
    .replace(/\n/g, '<br/>')
    .replace(/<ul><li>(.*?)<br\/>/g, '<ul><li>$1</li>')
    .replace(/<br\/>/g, '')
    .replace(/<ul><li>/g, '<ul><li>')
    .replace(/<\/li><li>/g, '</li><li>')
    .replace(/<\/li><\/ul>/g, '</li></ul>');
  return <div dangerouslySetInnerHTML={{ __html: html }} />;
} 