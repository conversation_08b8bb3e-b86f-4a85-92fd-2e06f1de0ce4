import React, { useState } from 'react';
import { FaMapMarkerAlt, FaCalendarAlt, FaShareAlt, FaCheckCircle } from 'react-icons/fa';

const job = {
  company: 'Hero MotoCorp Limited',
  logo: 'https://logo.clearbit.com/heromotocorp.com',
  title: 'Vehicle Functional Development',
  location: 'Jaipur',
  updated: 'Jul 22, 2025',
  daysLeft: 13,
  eligibility: 'Fresher',
  impressions: 2,
  deadline: '05 Aug 25, 12:00 AM IST',
  description: `The Engineer (VED Functional Development) is responsible for delivering optimized performance of vehicle systems...`,
  details: [
    'Coordinating, preparing, performing and/or supervising of chassis functional development activities.',
    'Engineering analysis of test results (both objective data and subjective feedback from test riders) to provide chassis development direction to achieve outstanding vehicle riding experiences.',
    'Maintain project delivery based on Quality/Cost/Time.',
    'Management of reporting system to ensure appropriate documentation of chassis development activities is provided within the projects timelines.'
  ],
  about: `Hero MotoCorp is India\'s leading two wheeler company...`,
};

const tabs = [
  'Job Description',
  'Dates & Deadlines',
  'Reviews',
  'FAQs & Discussions',
];

export default function JobDetails() {
  const [activeTab, setActiveTab] = useState(tabs[0]);

  return (
    <div className="bg-orange-50 min-h-screen py-10 px-2 md:px-8 flex flex-col md:flex-row gap-8">
      {/* Main Content */}
      <div className="flex-1 max-w-3xl">
        <div className="flex items-center gap-6 mb-6">
          <img src={job.logo} alt={job.company} className="w-20 h-20 rounded-2xl bg-white shadow" />
          <div>
            <h1 className="text-3xl font-extrabold text-gray-800 mb-1">{job.title}</h1>
            <div className="flex items-center gap-3 text-gray-600 mb-1">
              <span>{job.company}</span>
              <FaMapMarkerAlt /> <span>{job.location}</span>
            </div>
            <div className="text-sm text-gray-500">Updated On: {job.updated}</div>
          </div>
        </div>
        <div className="flex gap-6 mb-6">
          <div className="bg-white rounded-xl shadow p-4 flex flex-col items-center w-24">
            <span className="text-3xl font-bold text-orange-600">{job.daysLeft}</span>
            <span className="text-xs text-gray-600">Days Left</span>
          </div>
        </div>
        {/* Tabs */}
        <div className="flex gap-4 mb-6 border-b border-gray-200">
          {tabs.map(tab => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-5 py-2 font-semibold border-b-2 transition-all duration-200 ${activeTab === tab ? 'border-orange-500 text-orange-700 bg-orange-100 rounded-t' : 'border-transparent text-gray-600 hover:text-orange-600'}`}
            >
              {tab}
            </button>
          ))}
        </div>
        {/* Tab Content */}
        {activeTab === 'Job Description' && (
          <div className="bg-white rounded-xl shadow p-6 mb-6">
            <h2 className="text-xl font-bold mb-3 text-gray-800">Details</h2>
            <div className="mb-3 text-gray-700">{job.description}</div>
            <ul className="list-disc pl-6 text-gray-700 space-y-1">
              {job.details.map((d, i) => <li key={i}>{d}</li>)}
            </ul>
          </div>
        )}
        {activeTab === 'Dates & Deadlines' && (
          <div className="bg-white rounded-xl shadow p-6 mb-6">
            <h2 className="text-lg font-bold mb-3 text-gray-800">Important dates & deadlines?</h2>
            <div className="flex items-center gap-3 mb-2"><FaCalendarAlt className="text-orange-500" /> <span className="font-semibold">Application Deadline</span></div>
            <div className="ml-8 text-gray-700">{job.deadline}</div>
          </div>
        )}
        {activeTab === 'Reviews' && (
          <div className="bg-white rounded-xl shadow p-6 mb-6">No reviews yet.</div>
        )}
        {activeTab === 'FAQs & Discussions' && (
          <div className="bg-white rounded-xl shadow p-6 mb-6">No FAQs or discussions yet.</div>
        )}
      </div>
      {/* Sidebar */}
      <div className="w-full md:w-96 flex flex-col gap-6">
        <div className="bg-white rounded-xl shadow p-6 flex flex-col gap-3">
          <div className="font-bold text-lg">Abhishek E B</div>
          <div className="text-gray-500 text-sm"><EMAIL></div>
          <div className="flex items-center gap-2 mt-2">
            <FaCheckCircle className="text-green-500" /> <span className="text-green-700 font-semibold">Eligible</span>
          </div>
          <div className="flex gap-2 mt-4">
            <button className="bg-gray-100 rounded-full p-2 hover:bg-gray-200"><span role="img" aria-label="save">♡</span></button>
            <button className="bg-gray-100 rounded-full p-2 hover:bg-gray-200"><span role="img" aria-label="calendar">📅</span></button>
            <button className="bg-gray-100 rounded-full p-2 hover:bg-gray-200"><FaShareAlt /></button>
          </div>
          <button className="bg-blue-600 text-white font-bold py-3 rounded-xl mt-4 hover:bg-blue-700 transition">Apply</button>
        </div>
        <div className="bg-white rounded-xl shadow p-6 flex flex-col gap-2">
          <div className="text-gray-500 text-sm">Impressions</div>
          <div className="font-bold text-lg">{job.impressions}</div>
        </div>
        <div className="bg-white rounded-xl shadow p-6">
          <div className="font-bold mb-1">Eligibility</div>
          <div className="text-gray-700">{job.eligibility}</div>
        </div>
        {/* <div className="bg-white rounded-xl shadow p-6">
          <div className="font-bold mb-1">Refer & Win</div>
          <div className="text-gray-700 mb-2">MacBook, iPhone, Apple Watch, Cash and more!</div>
          <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded font-semibold hover:bg-blue-200 transition mb-1">Refer now</button>
          <button className="text-blue-700 font-semibold hover:underline">Know more</button>
        </div> */}
        {/* <div className="bg-white rounded-xl shadow p-6 flex flex-col items-center">
          <div className="font-bold text-purple-700 mb-1">Unstop Pro</div>
          <div className="text-gray-700 mb-2">50+ Courses, 15% Off Mentorship, 100+ Interview prep</div>
          <button className="bg-purple-600 text-white px-4 py-2 rounded font-bold hover:bg-purple-700 transition">Go Pro Now</button>
        </div> */}
      </div>
    </div>
  );
} 