import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaMapMarkerAlt, FaCalendarAlt, FaShareAlt, FaCheckCircle, FaSpinner, FaExclamationTriangle, FaBriefcase, FaClock, FaUsers, FaDollarSign, FaArrowLeft, FaBuilding, FaGraduationCap, FaLaptop } from 'react-icons/fa';
import useStudentStore from '../../store/studentStore';
import toast from 'react-hot-toast';

const tabs = [
  'Job Description',
  'Company Info',
  'Requirements',
  'Application Process',
];

export default function JobDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(tabs[0]);
  const [isApplying, setIsApplying] = useState(false);

  const {
    jobDetails,
    loading,
    error,
    profile,
    fetchJobDetails,
    applyToJob
  } = useStudentStore();

  // Fetch job details on component mount
  useEffect(() => {
    if (id) {
      fetchJobDetails(id);
    }
  }, [id, fetchJobDetails]);

  // Handle job application
  const handleApply = async () => {
    if (!jobDetails?.canApply) {
      toast.error('You cannot apply to this job');
      return;
    }

    setIsApplying(true);
    try {
      const result = await applyToJob(id);
      if (result) {
        toast.success('Application submitted successfully!');
        // Refresh job details to update application status
        await fetchJobDetails(id);
      }
    } catch (error) {
      toast.error(error.message || 'Failed to apply to job');
    } finally {
      setIsApplying(false);
    }
  };

  // Calculate days left until deadline
  const getDaysLeft = (deadline) => {
    if (!deadline) return 0;
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  // Format salary
  const formatSalary = (salary) => {
    if (!salary) return 'Not specified';
    if (typeof salary === 'object') {
      return `${salary.currency || 'INR'} ${salary.min || 0} - ${salary.max || 0}`;
    }
    return salary;
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Job Details...</h2>
          <p className="text-gray-500">Please wait while we fetch the job information.</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <FaExclamationTriangle className="text-4xl text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Job</h2>
          <p className="text-gray-500 mb-4">{error}</p>
          <div className="flex gap-3 justify-center">
            <button
              onClick={() => fetchJobDetails(id)}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition"
            >
              Try Again
            </button>
            <button
              onClick={() => navigate('/student/jobs')}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition"
            >
              Back to Jobs
            </button>
          </div>
        </div>
      </div>
    );
  }

  // No job data
  if (!jobDetails?.job) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FaBriefcase className="text-6xl text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Job Not Found</h2>
          <p className="text-gray-500 mb-4">The job you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => navigate('/student/jobs')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition"
          >
            Back to Jobs
          </button>
        </div>
      </div>
    );
  }

  const { job, hasApplied, applicationStatus, canApply } = jobDetails;
  const daysLeft = getDaysLeft(job.applicationDeadline);

  return (
    <div className="bg-gray-50 min-h-screen py-6 px-4 md:px-8">
      {/* Back Button */}
      <button
        onClick={() => navigate('/student/jobs')}
        className="flex items-center gap-2 text-blue-600 hover:text-blue-800 font-medium mb-6 transition"
      >
        <FaArrowLeft /> Back to Jobs
      </button>

      <div className="flex flex-col lg:flex-row gap-8 max-w-7xl mx-auto">
        {/* Main Content */}
        <div className="flex-1">
          {/* Job Header */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="flex items-start gap-6 mb-4">
              {job.companyId?.logo ? (
                <img
                  src={job.companyId.logo}
                  alt={job.companyId.name}
                  className="w-20 h-20 rounded-2xl bg-white shadow object-cover"
                />
              ) : (
                <div className="w-20 h-20 rounded-2xl bg-blue-100 shadow flex items-center justify-center">
                  <FaBuilding className="text-blue-600 text-2xl" />
                </div>
              )}
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">{job.title}</h1>
                <div className="flex items-center gap-4 text-gray-600 mb-2">
                  <div className="flex items-center gap-1">
                    <FaBuilding className="text-sm" />
                    <span className="font-medium">{job.companyId?.name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <FaMapMarkerAlt className="text-sm" />
                    <span>{job.location}</span>
                  </div>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <span>Updated: {new Date(job.updatedAt).toLocaleDateString()}</span>
                  <span>•</span>
                  <span>{job.currentApplications || 0} applications</span>
                </div>
              </div>
            </div>

            {/* Job Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{daysLeft}</div>
                <div className="text-sm text-gray-600">Days Left</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{job.experienceLevel}</div>
                <div className="text-sm text-gray-600">Experience</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{job.workMode}</div>
                <div className="text-sm text-gray-600">Work Mode</div>
              </div>
              <div className="bg-orange-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{job.jobType}</div>
                <div className="text-sm text-gray-600">Job Type</div>
              </div>
            </div>
          </div>
          {/* Tabs */}
          <div className="bg-white rounded-xl shadow-lg mb-6">
            <div className="flex border-b border-gray-200">
              {tabs.map(tab => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`flex-1 px-6 py-4 font-medium transition-all duration-200 ${
                    activeTab === tab
                      ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === 'Job Description' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-gray-800">Job Description</h2>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 mb-4 leading-relaxed">{job.description}</p>

                    {job.requirements && job.requirements.length > 0 && (
                      <div className="mt-6">
                        <h3 className="text-lg font-semibold mb-3 text-gray-800">Key Requirements</h3>
                        <ul className="list-disc pl-6 text-gray-700 space-y-2">
                          {job.requirements.map((req, i) => (
                            <li key={i}>{req}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {job.techStack && job.techStack.length > 0 && (
                      <div className="mt-6">
                        <h3 className="text-lg font-semibold mb-3 text-gray-800">Tech Stack</h3>
                        <div className="flex flex-wrap gap-2">
                          {job.techStack.map((tech, i) => (
                            <span key={i} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {activeTab === 'Company Info' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-gray-800">About {job.companyId?.name}</h2>
                  <div className="space-y-4">
                    {job.companyId?.description && (
                      <p className="text-gray-700 leading-relaxed">{job.companyId.description}</p>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                      {job.companyId?.website && (
                        <div className="flex items-center gap-3">
                          <FaGlobe className="text-blue-500" />
                          <div>
                            <div className="font-medium text-gray-800">Website</div>
                            <a
                              href={job.companyId.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {job.companyId.website}
                            </a>
                          </div>
                        </div>
                      )}

                      {job.companyId?.location && (
                        <div className="flex items-center gap-3">
                          <FaMapMarkerAlt className="text-red-500" />
                          <div>
                            <div className="font-medium text-gray-800">Location</div>
                            <div className="text-gray-600">
                              {job.companyId.location.city}, {job.companyId.location.state}, {job.companyId.location.country}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'Requirements' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-gray-800">Job Requirements</h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <FaGraduationCap className="text-blue-500" />
                          <span className="font-medium text-gray-800">Experience Level</span>
                        </div>
                        <div className="text-gray-700">{job.experienceLevel}</div>
                      </div>

                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <FaLaptop className="text-green-500" />
                          <span className="font-medium text-gray-800">Work Mode</span>
                        </div>
                        <div className="text-gray-700">{job.workMode}</div>
                      </div>

                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <FaClock className="text-orange-500" />
                          <span className="font-medium text-gray-800">Job Type</span>
                        </div>
                        <div className="text-gray-700">{job.jobType}</div>
                      </div>

                      {job.salary && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <FaDollarSign className="text-purple-500" />
                            <span className="font-medium text-gray-800">Salary</span>
                          </div>
                          <div className="text-gray-700">{formatSalary(job.salary)}</div>
                        </div>
                      )}
                    </div>

                    {job.requirements && job.requirements.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold mb-3 text-gray-800">Detailed Requirements</h3>
                        <ul className="list-disc pl-6 text-gray-700 space-y-2">
                          {job.requirements.map((req, i) => (
                            <li key={i}>{req}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {activeTab === 'Application Process' && (
                <div>
                  <h2 className="text-xl font-bold mb-4 text-gray-800">Application Process</h2>
                  <div className="space-y-6">
                    <div className="bg-blue-50 rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <FaCalendarAlt className="text-blue-500" />
                        <span className="font-semibold text-gray-800">Application Deadline</span>
                      </div>
                      <div className="text-gray-700">
                        {new Date(job.applicationDeadline).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </div>

                    <div className="bg-green-50 rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <FaUsers className="text-green-500" />
                        <span className="font-semibold text-gray-800">Application Status</span>
                      </div>
                      <div className="text-gray-700">
                        {job.currentApplications || 0} of {job.maxApplications} applications received
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{ width: `${((job.currentApplications || 0) / job.maxApplications) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    {job.hasTest && (
                      <div className="bg-yellow-50 rounded-lg p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <FaGraduationCap className="text-yellow-500" />
                          <span className="font-semibold text-gray-800">Assessment Required</span>
                        </div>
                        <div className="text-gray-700">
                          This position requires completing an assessment test after application.
                        </div>
                      </div>
                    )}

                    {hasApplied && applicationStatus && (
                      <div className="bg-purple-50 rounded-lg p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <FaCheckCircle className="text-purple-500" />
                          <span className="font-semibold text-gray-800">Your Application Status</span>
                        </div>
                        <div className="text-gray-700">
                          Status: <span className="font-medium capitalize">{applicationStatus.status.replace('_', ' ')}</span>
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          Applied on: {new Date(applicationStatus.appliedAt).toLocaleDateString()}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="w-full lg:w-96 flex flex-col gap-6">
          {/* User Profile Card */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center gap-4 mb-4">
              {profile?.avatar ? (
                <img
                  src={profile.avatar}
                  alt={profile.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-lg">
                    {profile?.name?.charAt(0)?.toUpperCase() || 'U'}
                  </span>
                </div>
              )}
              <div>
                <div className="font-bold text-lg text-gray-800">{profile?.name || 'User'}</div>
                <div className="text-gray-500 text-sm">{profile?.email || '<EMAIL>'}</div>
              </div>
            </div>

            {/* Application Status */}
            {hasApplied ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <FaCheckCircle className="text-green-500" />
                  <span className="text-green-700 font-semibold">Application Submitted</span>
                </div>
                <div className="text-sm text-green-600">
                  Status: <span className="font-medium capitalize">{applicationStatus?.status?.replace('_', ' ')}</span>
                </div>
                <div className="text-xs text-green-600 mt-1">
                  Applied: {new Date(applicationStatus?.appliedAt).toLocaleDateString()}
                </div>
              </div>
            ) : canApply ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <FaCheckCircle className="text-blue-500" />
                  <span className="text-blue-700 font-semibold">Eligible to Apply</span>
                </div>
                <div className="text-sm text-blue-600">
                  You meet the requirements for this position
                </div>
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <FaExclamationTriangle className="text-red-500" />
                  <span className="text-red-700 font-semibold">Cannot Apply</span>
                </div>
                <div className="text-sm text-red-600">
                  {job.currentApplications >= job.maxApplications
                    ? 'Maximum applications reached'
                    : 'Application deadline passed or job inactive'
                  }
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 mb-4">
              <button
                className="flex-1 bg-gray-100 rounded-lg p-2 hover:bg-gray-200 transition"
                title="Save Job"
              >
                ♡
              </button>
              <button
                className="flex-1 bg-gray-100 rounded-lg p-2 hover:bg-gray-200 transition"
                title="Add to Calendar"
              >
                📅
              </button>
              <button
                className="flex-1 bg-gray-100 rounded-lg p-2 hover:bg-gray-200 transition"
                title="Share Job"
              >
                <FaShareAlt />
              </button>
            </div>

            {/* Apply Button */}
            {hasApplied ? (
              <button
                disabled
                className="w-full bg-green-600 text-white font-bold py-3 rounded-xl cursor-not-allowed opacity-75"
              >
                <FaCheckCircle className="inline mr-2" />
                Applied
              </button>
            ) : canApply ? (
              <button
                onClick={handleApply}
                disabled={isApplying}
                className="w-full bg-blue-600 text-white font-bold py-3 rounded-xl hover:bg-blue-700 transition disabled:opacity-75 disabled:cursor-not-allowed"
              >
                {isApplying ? (
                  <>
                    <FaSpinner className="inline mr-2 animate-spin" />
                    Applying...
                  </>
                ) : (
                  <>
                    <FaBriefcase className="inline mr-2" />
                    Apply Now
                  </>
                )}
              </button>
            ) : (
              <button
                disabled
                className="w-full bg-gray-400 text-white font-bold py-3 rounded-xl cursor-not-allowed"
              >
                Cannot Apply
              </button>
            )}
          </div>

          {/* Job Stats */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="font-bold text-lg mb-4 text-gray-800">Job Statistics</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Applications</span>
                <span className="font-semibold text-gray-800">{job.currentApplications || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Max Applications</span>
                <span className="font-semibold text-gray-800">{job.maxApplications}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Views</span>
                <span className="font-semibold text-gray-800">{job.views || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Days Left</span>
                <span className="font-semibold text-gray-800">{daysLeft}</span>
              </div>
            </div>
          </div>

          {/* Quick Info */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="font-bold text-lg mb-4 text-gray-800">Quick Info</h3>
            <div className="space-y-3">
              <div>
                <div className="text-sm text-gray-600">Category</div>
                <div className="font-medium text-gray-800">{job.category}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Experience Level</div>
                <div className="font-medium text-gray-800">{job.experienceLevel}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Work Mode</div>
                <div className="font-medium text-gray-800">{job.workMode}</div>
              </div>
              {job.salary && (
                <div>
                  <div className="text-sm text-gray-600">Salary</div>
                  <div className="font-medium text-gray-800">{formatSalary(job.salary)}</div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}