import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaBriefcase, FaBullhorn, FaRupeeSign, FaChartBar, FaMapMarkerAlt, FaEye, FaArrowRight, FaLaptopCode, FaPalette } from 'react-icons/fa';
import { jobs } from './jobsData';

const categories = [
  { label: 'Software Development', icon: <FaLaptopCode /> },
  { label: 'Data Science', icon: <FaChartBar /> },
  { label: 'Graphic Design', icon: <FaPalette /> },
  { label: 'Marketing', icon: <FaBullhorn /> },
  { label: 'Finance', icon: <FaRupeeSign /> },
];


export default function Jobs() {
  const [selectedCategory, setSelectedCategory] = useState(categories[0].label);
  const navigate = useNavigate();

  const handleCategoryClick = (catLabel) => {
    setSelectedCategory(catLabel);
    navigate(`/student/jobs/category/${encodeURIComponent(catLabel)}`);
  };

  return (
    <div className="pb-20">
      {/* Hero Section */}
      <div className="flex flex-col md:flex-row items-center justify-between gap-8 mt-8 mb-12">
        <div className="flex-1">
          <h1 className="text-5xl font-extrabold mb-4 text-gray-800">
            <span className="text-[#23414c]">Unlock</span> Ambition
          </h1>
          <p className="text-lg text-gray-500 mb-6 max-w-xl">
            Grab your hiring opportunities & work with your dream companies!
          </p>
          <div className="flex gap-4 mb-6">
            <button className="bg-[#23414c] text-white font-bold px-8 py-3 rounded-full shadow hover:bg-[#23414c]/90 transition">Search your dream job below</button>
          </div>
        </div>
        <div className="flex-1 flex justify-center">
          <img src="/public/heroimage.jpg" alt="Hero" className="w-80 h-64 object-contain rounded-3xl shadow-lg" />
        </div>
      </div>

      {/* Category Bar */}
      <div className="mb-10">
        <div className="flex items-center gap-4 mb-4">
          <span className="font-semibold text-gray-700">Jobs Category</span>
          <div className="flex gap-3 overflow-x-auto pb-2">
            {categories.map(cat => (
              <button
                key={cat.label}
                onClick={() => handleCategoryClick(cat.label)}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl border-2 font-semibold whitespace-nowrap transition-all duration-200 ${selectedCategory === cat.label ? 'bg-[#23414c]/10 border-[#23414c] text-[#23414c] shadow' : 'bg-white border-gray-200 text-gray-600 hover:bg-[#23414c]/5 hover:border-[#23414c]/60'}`}
              >
                {cat.icon}
                {cat.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Jobs Section */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-800 mb-1">Jobs</h2>
          <div className="text-gray-500">Find jobs that fit your career aspirations.</div>
        </div>
        {/* View all button removed */}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pb-4">
        {jobs.map(job => (
          <Link key={job.id} to={`/jobs/${job.id}`} className={`relative rounded-2xl shadow-lg p-6 ${job.color} hover:shadow-2xl transition cursor-pointer group`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="bg-white text-xs font-bold px-3 py-1 rounded-full shadow mr-2">{job.mode}</span>
              {job.activelyHiring && <span className="bg-yellow-400 text-xs font-bold px-3 py-1 rounded-full shadow flex items-center gap-1">⚡ Actively Hiring</span>}
            </div>
            <div className="flex items-center gap-3 mb-4">
              <img src={job.logo} alt={job.company} className="w-12 h-12 rounded-xl bg-white shadow" />
              <span className="font-bold text-lg text-gray-800">{job.company}</span>
            </div>
            <div className="font-bold text-xl mb-2 text-gray-900">{job.title}</div>
            <div className="flex items-center gap-3 text-gray-600 text-sm mb-2">
              <FaEye /> {job.views} Views
            </div>
            <div className="flex items-center gap-2 text-gray-600 text-sm">
              <FaMapMarkerAlt /> {job.location}
            </div>
            <span className="absolute top-4 right-4 text-gray-400 group-hover:text-[#23414c] transition"><FaArrowRight /></span>
          </Link>
        ))}
      </div>
    </div>
  );
} 