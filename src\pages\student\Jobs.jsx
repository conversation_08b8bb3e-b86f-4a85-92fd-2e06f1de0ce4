import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaBriefcase, FaBullhorn, FaRupeeSign, FaChartBar, FaMapMarkerAlt, FaEye, FaArrowRight, FaLaptopCode, FaPalette, FaSpinner, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import useStudentStore from '../../store/studentStore';

const categories = [
  { label: 'Software Development', icon: <FaLaptopCode /> },
  { label: 'Data Science', icon: <FaChartBar /> },
  { label: 'Graphic Design', icon: <FaPalette /> },
  { label: 'Marketing', icon: <FaBullhorn /> },
  { label: 'Finance', icon: <FaRupeeSign /> },
];


export default function Jobs() {
  const [selectedCategory, setSelectedCategory] = useState(categories[0].label);
  const navigate = useNavigate();

  // Get data and functions from store
  const {
    jobs,
    jobsLoading,
    jobsError,
    jobsPagination,
    jobsFilter,
    fetchJobs
  } = useStudentStore();

  // Load jobs on component mount
  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  const handleCategoryClick = (catLabel) => {
    setSelectedCategory(catLabel);
    navigate(`/student/jobs/category/${encodeURIComponent(catLabel)}`);
  };

  // Pagination handlers
  const handlePageChange = (page) => {
    fetchJobs({ ...jobsFilter, page });
  };

  const handlePrevPage = () => {
    if (jobsPagination.hasPrevPage) {
      handlePageChange(jobsPagination.currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (jobsPagination.hasNextPage) {
      handlePageChange(jobsPagination.currentPage + 1);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const { currentPage, totalPages } = jobsPagination;
    const pages = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  return (
    <div className="pb-20">
      {/* Hero Section */}
      <div className="flex flex-col md:flex-row items-center justify-between gap-8 mt-8 mb-12">
        <div className="flex-1">
          <h1 className="text-5xl font-extrabold mb-4 text-gray-800">
            <span className="text-[#23414c]">Unlock</span> Ambition
          </h1>
          <p className="text-lg text-gray-500 mb-6 max-w-xl">
            Grab your hiring opportunities & work with your dream companies!
          </p>
          <div className="flex gap-4 mb-6">
            <button className="bg-[#23414c] text-white font-bold px-8 py-3 rounded-full shadow hover:bg-[#23414c]/90 transition">Search your dream job below</button>
          </div>
        </div>
        <div className="flex-1 flex justify-center">
          <img src="/public/heroimage.jpg" alt="Hero" className="w-80 h-64 object-contain rounded-3xl shadow-lg" />
        </div>
      </div>

      {/* Category Bar */}
      <div className="mb-10">
        <div className="flex items-center gap-4 mb-4">
          <span className="font-semibold text-gray-700">Jobs Category</span>
          <div className="flex gap-3 overflow-x-auto pb-2">
            {categories.map(cat => (
              <button
                key={cat.label}
                onClick={() => handleCategoryClick(cat.label)}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl border-2 font-semibold whitespace-nowrap transition-all duration-200 ${selectedCategory === cat.label ? 'bg-[#23414c]/10 border-[#23414c] text-[#23414c] shadow' : 'bg-white border-gray-200 text-gray-600 hover:bg-[#23414c]/5 hover:border-[#23414c]/60'}`}
              >
                {cat.icon}
                {cat.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Jobs Section */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-800 mb-1">Jobs</h2>
          <div className="text-gray-500">
            Find jobs that fit your career aspirations.
            {jobsPagination.totalJobs > 0 && (
              <span className="ml-2 text-blue-600 font-medium">
                ({jobsPagination.totalJobs} jobs available)
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Loading State */}
      {jobsLoading && (
        <div className="flex items-center justify-center py-12">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mr-3" />
          <span className="text-lg text-gray-600">Loading jobs...</span>
        </div>
      )}

      {/* Error State */}
      {jobsError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="text-red-800 font-medium">Error loading jobs</div>
          <div className="text-red-600 text-sm mt-1">{jobsError}</div>
          <button
            onClick={() => fetchJobs()}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Jobs Grid */}
      {!jobsLoading && !jobsError && (
        <>
          {jobs.length === 0 ? (
            <div className="text-center py-12">
              <FaBriefcase className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-600 mb-2">No jobs found</h3>
              <p className="text-gray-500">Check back later for new opportunities.</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pb-4">
                {jobs.map(job => (
                  <Link
                    key={job._id || job.id}
                    to={`/jobs/${job._id || job.id}`}
                    className="relative rounded-2xl shadow-lg p-6 bg-white hover:shadow-2xl transition cursor-pointer group border border-gray-100"
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <span className="bg-blue-100 text-blue-800 text-xs font-bold px-3 py-1 rounded-full shadow mr-2">
                        {job.workMode || job.mode || 'Full-time'}
                      </span>
                      {job.isActive && (
                        <span className="bg-green-100 text-green-800 text-xs font-bold px-3 py-1 rounded-full shadow flex items-center gap-1">
                          ⚡ Active
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-3 mb-4">
                      {job.companyLogo ? (
                        <img
                          src={job.companyLogo}
                          alt={job.companyName || job.company}
                          className="w-12 h-12 rounded-xl bg-white shadow object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-xl bg-blue-100 shadow flex items-center justify-center">
                          <FaBriefcase className="text-blue-600" />
                        </div>
                      )}
                      <span className="font-bold text-lg text-gray-800">
                        {job.companyName || job.company}
                      </span>
                    </div>
                    <div className="font-bold text-xl mb-2 text-gray-900">{job.title}</div>
                    <div className="flex items-center gap-3 text-gray-600 text-sm mb-2">
                      <FaEye /> {job.views || 0} Views
                    </div>
                    <div className="flex items-center gap-2 text-gray-600 text-sm">
                      <FaMapMarkerAlt /> {job.location}
                    </div>
                    {job.salary && (
                      <div className="mt-2 text-green-600 font-medium text-sm">
                        {job.salary}
                      </div>
                    )}
                    <span className="absolute top-4 right-4 text-gray-400 group-hover:text-blue-600 transition">
                      <FaArrowRight />
                    </span>
                  </Link>
                ))}
              </div>

              {/* Pagination */}
              {jobsPagination.totalPages > 1 && (
                <div className="mt-8 flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    Showing {((jobsPagination.currentPage - 1) * jobsPagination.limit) + 1} to{' '}
                    {Math.min(jobsPagination.currentPage * jobsPagination.limit, jobsPagination.totalJobs)} of{' '}
                    {jobsPagination.totalJobs} jobs
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      onClick={handlePrevPage}
                      disabled={!jobsPagination.hasPrevPage || jobsLoading}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                    >
                      <FaChevronLeft className="text-xs" />
                      Previous
                    </button>

                    <div className="flex items-center gap-1">
                      {getPageNumbers().map(page => (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          disabled={jobsLoading}
                          className={`px-3 py-2 text-sm rounded-lg disabled:opacity-50 ${
                            jobsPagination.currentPage === page
                              ? 'bg-blue-600 text-white'
                              : 'border border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                    </div>

                    <button
                      onClick={handleNextPage}
                      disabled={!jobsPagination.hasNextPage || jobsLoading}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                    >
                      Next
                      <FaChevronRight className="text-xs" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
} 